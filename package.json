{"name": "hodlhub-communities", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "clear": "expo start --clear", "tunnel": "expo start --tunnel", "build": "expo build", "build:android": "expo build:android", "build:ios": "expo build:ios"}, "dependencies": {"@expo/vector-icons": "^14.0.0", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-masked-view/masked-view": "^0.3.0", "expo": "^53.0.0", "expo-asset": "~11.0.1", "expo-blur": "~14.0.1", "expo-font": "~13.0.1", "expo-linear-gradient": "~14.0.1", "expo-router": "^4.0.0", "expo-splash-screen": "~0.29.13", "expo-status-bar": "~2.0.0", "expo-linking": "~7.0.5", "lottie-react-native": "^6.7.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "^0.76.0", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.1.0", "react-native-svg": "^15.8.0", "react-native-web": "~0.19.13"}, "devDependencies": {"@babel/core": "^7.25.0", "@types/react": "~18.2.79", "react-native-svg-transformer": "^1.5.1", "typescript": "~5.3.3"}, "overrides": {"react": "18.2.0", "react-dom": "18.2.0"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0"}, "private": true}